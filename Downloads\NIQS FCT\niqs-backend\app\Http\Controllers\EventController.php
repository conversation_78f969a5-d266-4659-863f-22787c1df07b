<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * EventController handles event-related functionality
 * This includes displaying events, event details, and event registration
 */
class EventController extends Controller
{
    /**
     * Display a listing of events
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        try {
            $events = collect();
            $upcomingEvents = collect();
            $pastEvents = collect();
            
            try {
                // Get all events with pagination
                $query = DB::table('events')
                    ->where('status', 'active')
                    ->orderBy('event_date', 'desc');
                
                // Apply search filter if provided
                if ($request->has('search') && !empty($request->search)) {
                    $searchTerm = '%' . $request->search . '%';
                    $query->where(function($q) use ($searchTerm) {
                        $q->where('title', 'LIKE', $searchTerm)
                          ->orWhere('description', 'LIKE', $searchTerm)
                          ->orWhere('location', 'LIKE', $searchTerm);
                    });
                }
                
                // Apply category filter if provided
                if ($request->has('category') && !empty($request->category)) {
                    $query->where('category', $request->category);
                }
                
                $events = $query->paginate(12);
                
                // Get upcoming events for sidebar
                $upcomingEvents = DB::table('events')
                    ->where('status', 'active')
                    ->where('event_date', '>=', now())
                    ->orderBy('event_date', 'asc')
                    ->limit(5)
                    ->get();
                
                // Get past events for reference
                $pastEvents = DB::table('events')
                    ->where('status', 'active')
                    ->where('event_date', '<', now())
                    ->orderBy('event_date', 'desc')
                    ->limit(5)
                    ->get();
                
                Log::info('Successfully fetched events for events page');
                
            } catch (\Exception $e) {
                Log::warning('Could not fetch events: ' . $e->getMessage());
            }
            
            return view('events.index', compact('events', 'upcomingEvents', 'pastEvents'));
            
        } catch (\Exception $e) {
            Log::error('Error in EventController@index: ' . $e->getMessage());
            return view('events.index', [
                'events' => collect(),
                'upcomingEvents' => collect(),
                'pastEvents' => collect()
            ]);
        }
    }
    
    /**
     * Display the specified event
     * 
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        try {
            $event = null;
            $relatedEvents = collect();
            
            try {
                // Get the specific event
                $event = DB::table('events')
                    ->where('id', $id)
                    ->where('status', 'active')
                    ->first();
                
                if (!$event) {
                    Log::warning('Event not found with ID: ' . $id);
                    return redirect()->route('events.index')
                        ->with('error', 'Event not found.');
                }
                
                // Get related events (same category, excluding current event)
                $relatedEvents = DB::table('events')
                    ->where('status', 'active')
                    ->where('category', $event->category)
                    ->where('id', '!=', $id)
                    ->orderBy('event_date', 'desc')
                    ->limit(3)
                    ->get();
                
                Log::info('Successfully fetched event details for ID: ' . $id);
                
            } catch (\Exception $e) {
                Log::warning('Could not fetch event details: ' . $e->getMessage());
                return redirect()->route('events.index')
                    ->with('error', 'Could not load event details.');
            }
            
            return view('events.show', compact('event', 'relatedEvents'));
            
        } catch (\Exception $e) {
            Log::error('Error in EventController@show: ' . $e->getMessage());
            return redirect()->route('events.index')
                ->with('error', 'An error occurred while loading the event.');
        }
    }
    
    /**
     * Display the event registration form
     * 
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function register($id)
    {
        try {
            $event = null;
            
            try {
                // Get the event for registration
                $event = DB::table('events')
                    ->where('id', $id)
                    ->where('status', 'active')
                    ->where('event_date', '>=', now()) // Only allow registration for future events
                    ->first();
                
                if (!$event) {
                    return redirect()->route('events.index')
                        ->with('error', 'Event not found or registration is closed.');
                }
                
            } catch (\Exception $e) {
                Log::warning('Could not fetch event for registration: ' . $e->getMessage());
                return redirect()->route('events.index')
                    ->with('error', 'Could not load event registration.');
            }
            
            return view('events.register', compact('event'));
            
        } catch (\Exception $e) {
            Log::error('Error in EventController@register: ' . $e->getMessage());
            return redirect()->route('events.index')
                ->with('error', 'An error occurred while loading the registration form.');
        }
    }
    
    /**
     * Handle event registration submission
     * 
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submitRegistration(Request $request, $id)
    {
        try {
            // Validate the registration data
            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'required|string|max:20',
                'organization' => 'nullable|string|max:255',
                'position' => 'nullable|string|max:255',
                'dietary_requirements' => 'nullable|string|max:500',
                'special_needs' => 'nullable|string|max:500'
            ]);
            
            try {
                // Check if event exists and is still open for registration
                $event = DB::table('events')
                    ->where('id', $id)
                    ->where('status', 'active')
                    ->where('event_date', '>=', now())
                    ->first();
                
                if (!$event) {
                    return redirect()->route('events.index')
                        ->with('error', 'Event not found or registration is closed.');
                }
                
                // Check if user is already registered
                $existingRegistration = DB::table('event_registrations')
                    ->where('event_id', $id)
                    ->where('email', $validatedData['email'])
                    ->first();
                
                if ($existingRegistration) {
                    return redirect()->route('events.show', $id)
                        ->with('warning', 'You are already registered for this event.');
                }
                
                // Save the registration
                DB::table('event_registrations')->insert([
                    'event_id' => $id,
                    'name' => $validatedData['name'],
                    'email' => $validatedData['email'],
                    'phone' => $validatedData['phone'],
                    'organization' => $validatedData['organization'] ?? null,
                    'position' => $validatedData['position'] ?? null,
                    'dietary_requirements' => $validatedData['dietary_requirements'] ?? null,
                    'special_needs' => $validatedData['special_needs'] ?? null,
                    'registration_date' => now(),
                    'status' => 'confirmed',
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
                
                Log::info('Event registration successful for event ID: ' . $id . ' by: ' . $validatedData['email']);
                
                return redirect()->route('events.show', $id)
                    ->with('success', 'Registration successful! You will receive a confirmation email shortly.');
                    
            } catch (\Exception $e) {
                Log::error('Could not save event registration: ' . $e->getMessage());
                
                return redirect()->route('events.register', $id)
                    ->with('error', 'Sorry, there was an error processing your registration. Please try again.');
            }
            
        } catch (\Exception $e) {
            Log::error('Error in EventController@submitRegistration: ' . $e->getMessage());
            
            return redirect()->route('events.register', $id)
                ->with('error', 'An error occurred while processing your registration.');
        }
    }
}
