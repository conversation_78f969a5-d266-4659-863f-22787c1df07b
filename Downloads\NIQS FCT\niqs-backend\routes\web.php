<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\EventController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Home page route
Route::get('/', [HomeController::class, 'index'])->name('home');

// API route for homepage statistics (can be called via AJAX)
Route::get('/api/statistics', [HomeController::class, 'getStatistics'])->name('api.statistics');

// About NIQS routes
Route::get('/about', [PageController::class, 'about'])->name('about');
Route::get('/chairman', [PageController::class, 'chairman'])->name('chairman');
Route::get('/senate', [PageController::class, 'senate'])->name('senate');
Route::get('/past-chairman', [PageController::class, 'pastChairman'])->name('past-chairman');

// Contact routes
Route::get('/contact', [PageController::class, 'contact'])->name('contact');
Route::post('/contact', [PageController::class, 'submitContact'])->name('contact.submit');

// Membership routes
Route::get('/membership', [PageController::class, 'membership'])->name('membership');

// Event routes
Route::get('/events', [EventController::class, 'index'])->name('events');
Route::get('/events/{id}', [EventController::class, 'show'])->name('events.show');
Route::get('/events/{id}/register', [EventController::class, 'register'])->name('events.register');
Route::post('/events/{id}/register', [EventController::class, 'submitRegistration'])->name('events.register.submit');

// Activities routes (Workshop, Examination, Certificate)
Route::get('/workshop', function () {
    return view('pages.workshop');
})->name('workshop');

Route::get('/examination', function () {
    return view('pages.examination');
})->name('examination');

Route::get('/certificate', function () {
    return view('pages.certificate');
})->name('certificate');

// Additional static pages
Route::get('/gallery', function () {
    return view('pages.gallery');
})->name('gallery');

Route::get('/blog', function () {
    return view('pages.blog');
})->name('blog');

Route::get('/register', function () {
    return view('pages.register');
})->name('register');
